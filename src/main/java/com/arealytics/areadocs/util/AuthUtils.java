package com.arealytics.areadocs.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.repository.UserRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Slf4j
public class AuthUtils {

    private final UserRepository userRepository;

    public String getLoggedInUsername() {
        Jwt jwt = getJwtToken();
        if (jwt != null) {
            return jwt.getClaimAsString("preferred_username");
        }
        return null;
    }

    public String getLoggedInEmail() {
        Jwt jwt = getJwtToken();
        if (jwt != null) {
            return jwt.getClaimAsString("email");
        }
        return null;
    }

    public Jwt getJwtToken() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof Jwt) {
            return (Jwt) authentication.getPrincipal();
        }
        return null;
    }

    public User getCurrentUser() {
        String email = getLoggedInEmail();
        if (email == null) {
            log.debug("No email found in JWT token or no authentication present");
            return null; // No authentication or no email claim
        }

        User user = userRepository.findByEmail(email).orElse(null);
        if (user == null) {
            log.warn(
                    "User not found in database for email: {}. This user exists in Keycloak but not"
                            + " in the application database. Please create this user via the user"
                            + " management API or add to migration script.",
                    email);
        }
        return user;
    }

    @Transactional(readOnly = true)
    public Company getCurrentUserCompany() {
        User user = getCurrentUser();

        if (user.getUserType() != UserType.COMPANY) {
            // Not a company user, return null
            return null;
        }

        CompanyMembership membership = user.getCompanyMemberships();
        if (membership == null) {
            throw new CompanyException("Company membership not found for the current user");
        }

        Company company = membership.getCompany();
        if (company == null) {
            throw new CompanyException("Company details not found for the current user");
        }

        return Boolean.TRUE.equals(company.getIsActive()) ? company : null;
    }

    public Long getCurrentUserCompanyId() {
        Company company = getCurrentUserCompany();
        return company != null ? company.getId() : null;
    }
}
