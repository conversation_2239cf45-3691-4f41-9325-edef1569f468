package com.arealytics.areadocs.service;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.Folder;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.FolderRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.FolderTreeDTO;
import com.arealytics.areadocs.enumeration.FolderType;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.*;
import com.arealytics.areadocs.repository.DocumentRepository;
import com.arealytics.areadocs.repository.FolderRepository;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class FolderService {

    private final FolderRepository folderRepository;
    private final DocumentRepository documentRepository;

    /**
     * Create a new folder
     *
     * @param requestDTO The folder request data
     * @param user The user creating the folder
     * @param company The company the folder belongs to
     * @return The created folder
     */
    public Folder createFolder(FolderRequestDTO requestDTO, User user, Company company) {
        validateFolderNameUniqueness(
                user.getId(),
                company != null ? company.getId() : null,
                requestDTO.getName(),
                requestDTO.getParentFolderId());

        Folder parentFolder =
                requestDTO.getParentFolderId() != null
                        ? getFolderByIdWithValidation(requestDTO.getParentFolderId(), user, company)
                        : getRootFolderForUser(user.getId());

        try {
            Folder folder = new Folder();
            folder.setName(requestDTO.getName());
            folder.setUser(user);
            folder.setParentFolder(parentFolder);
            folder.setFolderType(FolderType.SHARED);

            if (user.getUserType() == UserType.COMPANY && company != null) {
                folder.setCompany(company);
            }

            return folderRepository.save(folder);
        } catch (Exception e) {
            log.error("Error creating folder: {}", e.getMessage(), e);
            throw new FolderCreationException("Failed to create folder: " + e.getMessage(), e);
        }
    }

    /**
     * Update an existing folder
     *
     * @param id The folder ID
     * @param requestDTO The updated folder data
     * @param user The user updating the folder
     * @param company The company the folder belongs to
     * @return The updated folder
     */
    public Folder updateFolder(Long id, FolderRequestDTO requestDTO, User user, Company company) {
        Folder existingFolder = getFolderById(id);

        if (isSystemFolder(existingFolder)) {
            throw new UnsupportedOperationException("Cannot update system folder");
        }

        validateFolderOwnership(existingFolder, user, company);

        if (!existingFolder.getName().equals(requestDTO.getName())) {
            validateFolderNameUniqueness(
                    user.getId(),
                    company != null ? company.getId() : null,
                    requestDTO.getName(),
                    requestDTO.getParentFolderId());
        }

        Folder parentFolder =
                resolveAndValidateParentFolder(requestDTO, existingFolder, user, company);
        updateFolderFields(existingFolder, requestDTO, user, company, parentFolder);

        return folderRepository.save(existingFolder);
    }

    /**
     * Delete a folder
     *
     * @param id The folder ID
     */
    public void deleteFolder(Long id) {
        Folder folder = getFolderById(id);

        if (isSystemFolder(folder)) {
            throw new UnsupportedOperationException("Cannot delete system folder");
        }

        if (hasChildFolders(id)) {
            throw new FolderDeletionException("Cannot delete folder with sub-folders");
        }

        if (hasDocuments(id)) {
            throw new FolderDeletionException("Cannot delete folder containing documents");
        }

        try {
            folderRepository.delete(folder);
            log.info("Folder deleted successfully: {}", id);
        } catch (Exception e) {
            log.error("Error deleting folder with id {}: {}", id, e.getMessage(), e);
            throw new FolderDeletionException("Failed to delete folder: " + e.getMessage(), e);
        }
    }

    /**
     * Move a folder to a new parent folder
     *
     * @param id The folder ID
     * @param newParentId The new parent folder ID
     * @return The moved folder
     */
    public Folder moveFolder(Long id, Long newParentId) {
        Folder folder = getFolderById(id);
        Folder newParent = getFolderById(newParentId);
        folder.setParentFolder(newParent);
        return folderRepository.save(folder);
    }

    /**
     * Create a system folder
     *
     * @param user The user creating the folder
     * @param company The company the folder belongs to
     * @param folderType The folder type
     * @param parentFolderId The parent folder ID
     * @return The created folder
     */
    public Folder createSystemFolder(
            User user, Company company, FolderType folderType, Long parentFolderId) {
        if (user == null && company == null) {
            log.error("Both user and company are null — cannot create system folder");
            throw new IllegalArgumentException("Either user or company must be provided");
        }

        validateSystemFolderCreation(user, company, folderType);

        try {
            Folder folder = new Folder();
            folder.setName(getSystemFolderName(folderType));
            folder.setUser(user);
            folder.setCompany(company);
            folder.setFolderType(folderType);
            folder.setParentFolder(parentFolderId != null ? getFolderById(parentFolderId) : null);
            return folderRepository.save(folder);
        } catch (Exception e) {
            throw new SystemFolderCreationException("Failed to create system folder", e);
        }
    }

    /**
     * Get all folders for a user
     *
     * @param userId The user ID
     * @return A list of folders
     */
    public List<Folder> getFoldersByUserId(Long userId) {
        return folderRepository.findByUser_IdOrderByNameAsc(userId);
    }

    /**
     * Get all folders for a company
     *
     * @param companyId The company ID
     * @return A list of folders
     */
    public List<Folder> getFoldersByCompanyId(Long companyId) {
        return folderRepository.findByCompany_IdOrderByNameAsc(companyId);
    }

    /**
     * Get the folder tree for a user
     *
     * @param rootFolder The root folder
     * @param user The user
     * @return The folder tree
     */
    public FolderTreeDTO getFolderTree(Folder rootFolder, User user) {
        return buildTreeRecursive(rootFolder, user);
    }

    /**
     * Build the folder tree recursively
     *
     * @param folder The current folder
     * @param user The user
     * @return The folder tree
     */
    private FolderTreeDTO buildTreeRecursive(Folder folder, User user) {
        List<Folder> childFolders = folderRepository.findByParentFolder_Id(folder.getId());

        List<FolderTreeDTO> childTrees =
                childFolders.stream()
                        .map(child -> buildTreeRecursive(child, user))
                        .collect(Collectors.toList());

        FolderTreeDTO tree = new FolderTreeDTO();
        tree.setId(folder.getId());
        tree.setName(folder.getName());
        tree.setFolderType(folder.getFolderType());
        tree.setChildren(childTrees);
        tree.setChildCount(childTrees.size());

        return tree;
    }

    /**
     * Get all child folders for a parent folder
     *
     * @param parentFolderId The parent folder ID
     * @param user The user
     * @param company The company
     * @return A list of child folders
     */
    public List<Folder> getChildFolders(Long parentFolderId, User user, Company company) {
        Folder parentFolder = getFolderById(parentFolderId);
        validateFolderOwnership(parentFolder, user, company);
        return folderRepository.findByParentFolder_IdAndUser_IdOrderByNameAsc(
                parentFolderId, user.getId());
    }

    /**
     * Get the root folder for a user
     *
     * @param userId The user ID
     * @return The root folder
     */
    public Folder getRootFolderForUser(Long userId) {
        return folderRepository.findByUser_IdAndFolderType(userId, FolderType.ROOT).orElse(null);
    }

    /**
     * Get a folder by ID
     *
     * @param id The folder ID
     * @return The folder
     */
    public Folder getFolderById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("Folder ID cannot be null");
        }

        return folderRepository
                .findById(id)
                .orElseThrow(
                        () ->
                                new DocumentFolderNotFoundException(
                                        "Folder not found with id: " + id));
    }

    /**
     * Get a folder by ID and validate ownership
     *
     * @param id The folder ID
     * @param user The user
     * @param company The company
     * @return The folder
     */
    private Folder getFolderByIdWithValidation(Long id, User user, Company company) {
        Folder folder = getFolderById(id);
        validateFolderOwnership(folder, user, company);
        return folder;
    }

    private void validateSystemFolderCreation(User user, Company company, FolderType folderType) {
        if (folderType == null) {
            throw new IllegalArgumentException("FolderType must not be null");
        }

        boolean exists = false;

        if (user != null) {
            exists = folderRepository.existsByUserAndFolderType(user, folderType);
        }

        if (!exists && company != null) {
            exists = folderRepository.existsByCompanyAndFolderType(company, folderType);
        }

        if (exists) {
            throw new IllegalStateException(
                    "System folder already exists for the given user or company.");
        }
    }

    /**
     * Validate folder ownership
     *
     * @param folder The folder
     * @param user The user
     * @param company The company
     */
    private void validateFolderOwnership(Folder folder, User user, Company company) {
        if (folder.getUser() != null && !folder.getUser().getId().equals(user.getId())) {
            throw new UnauthorizedException("User does not have access to folder");
        }
        if (folder.getCompany() != null
                && (company == null || !folder.getCompany().getId().equals(company.getId()))) {
            throw new UnauthorizedException("Company does not have access to folder");
        }
    }

    /**
     * Validate folder name uniqueness
     *
     * @param userId The user ID
     * @param companyId The company ID
     * @param name The folder name
     * @param parentFolderId The parent folder ID
     */
    private void validateFolderNameUniqueness(
            Long userId, Long companyId, String name, Long parentFolderId) {
        boolean exists;

        if (parentFolderId != null) {
            exists =
                    folderRepository.existsByUser_IdAndNameAndParentFolder_Id(
                            userId, name, parentFolderId);
        } else if (companyId != null) {
            exists = folderRepository.existsByCompany_IdAndName(companyId, name);
        } else {
            exists = folderRepository.existsByUser_IdAndName(userId, name);
        }

        if (exists) {
            throw new FolderNameConflictException(
                    "Folder with name '" + name + "' already exists in this location");
        }
    }

    /**
     * Check if a folder is a system folder
     *
     * @param folder The folder
     * @return true if the folder is a system folder, false otherwise
     */
    private boolean isSystemFolder(Folder folder) {
        return folder.getFolderType() == FolderType.ROOT;
    }

    /**
     * Check if a folder has child folders
     *
     * @param folderId The folder ID
     * @return true if the folder has child folders, false otherwise
     */
    private boolean hasChildFolders(Long folderId) {
        return folderRepository.existsByParentFolder_Id(folderId);
    }

    /**
     * Check if a folder has documents
     *
     * @param folderId The folder ID
     * @return true if the folder has documents, false otherwise
     */
    private boolean hasDocuments(Long folderId) {
        return documentRepository.existsByFolder_Id(folderId);
    }

    /**
     * Get the system folder name
     *
     * @param folderType The folder type
     * @return The system folder name
     */
    private String getSystemFolderName(FolderType folderType) {
        return switch (folderType) {
            case ROOT -> "Root";
            case DOWNLOADS -> "Downloads";
            case SHARED -> "Shared";
            default -> folderType.name();
        };
    }

    /**
     * Resolve and validate the parent folder
     *
     * @param requestDTO The folder request DTO
     * @param existingFolder The existing folder
     * @param user The user
     * @param company The company
     * @return The parent folder
     */
    private Folder resolveAndValidateParentFolder(
            FolderRequestDTO requestDTO, Folder existingFolder, User user, Company company) {
        if (requestDTO.getParentFolderId() != null) {
            Folder newParent = getFolderById(requestDTO.getParentFolderId());
            validateFolderOwnership(newParent, user, company);
            return newParent;
        } else {
            return existingFolder.getParentFolder();
        }
    }

    /**
     * Update the folder fields
     *
     * @param folder The folder
     * @param requestDTO The folder request DTO
     * @param user The user
     * @param company The company
     * @param parentFolder The parent folder
     */
    private void updateFolderFields(
            Folder folder,
            FolderRequestDTO requestDTO,
            User user,
            Company company,
            Folder parentFolder) {
        folder.setName(requestDTO.getName());
        folder.setParentFolder(parentFolder);
        folder.setUser(user);
        folder.setCompany(company);
    }

    /**
     * Check if a user has access to a folder
     *
     * @param user The user
     * @param company The company
     * @param folder The folder
     * @return true if the user has access to the folder, false otherwise
     */
    public boolean hasWriteAccessToFolder(User user, Company company, Folder folder) {
        // Deny access to ROOT and DOWNLOADS folders
        if (folder.getFolderType() == FolderType.ROOT) {
            return true;
        }

        // Restrict INDIVIDUAL users to their own folders only
        if (user.getUserType() == UserType.INDIVIDUAL) {
            return folder.getUser() == null || !folder.getUser().getId().equals(user.getId());
        }

        // For COMPANY users:
        // 1. If folder is user-owned and matches
        if (folder.getUser() != null && folder.getUser().getId().equals(user.getId())) {
            return false;
        }

        // 2. If folder is company-owned and user's membership matches
        if (folder.getCompany() != null) {
            Long folderCompanyId = folder.getCompany().getId();

            // If user has a membership and matches
            if (user.getCompanyMemberships() != null
                    && user.getCompanyMemberships().getCompany() != null
                    && folderCompanyId.equals(user.getCompanyMemberships().getCompany().getId())) {
                return false;
            }

            // Or passed company matches
            if (company != null && folderCompanyId.equals(company.getId())) {
                return false;
            }
        }

        // Allow COMPANY_ADMIN role access
        return !isAdminUser(user);
    }

    /**
     * Check if a user has read access to a folder
     *
     * @param user The user
     * @param company The company
     * @param folder The folder
     * @return true if the user has read access to the folder, false otherwise
     */
    public boolean hasReadAccessToFolder(User user, Company company, Folder folder) {
        // Restrict INDIVIDUAL users to their own folders only
        if (user.getUserType() == UserType.INDIVIDUAL) {
            return folder.getUser() == null || !folder.getUser().getId().equals(user.getId());
        }

        // For COMPANY users:
        // 1. If the folder is user-owned and matches
        if (folder.getUser() != null && folder.getUser().getId().equals(user.getId())) {
            return true;
        }

        // 2. If folder is company-owned and user's membership matches
        if (folder.getCompany() != null) {
            Long folderCompanyId = folder.getCompany().getId();

            // If user has a membership and matches
            if (user.getCompanyMemberships() != null
                    && user.getCompanyMemberships().getCompany() != null
                    && folderCompanyId.equals(user.getCompanyMemberships().getCompany().getId())) {
                return true;
            }

            // Or passed company matches
            if (company != null && folderCompanyId.equals(company.getId())) {
                return true;
            }
        }

        // Allow COMPANY_ADMIN role access
        return isAdminUser(user);
    }

    /**
     * Check if a user is an admin
     *
     * @param user The user
     * @return true if the user is an admin, false otherwise
     */
    private boolean isAdminUser(User user) {
        return user != null
                && user.getUserRoles().stream()
                        .anyMatch(role -> role.getRole().getName().equals("COMPANY_ADMIN"));
    }

    /**
     * Find a folder by company and folder type
     *
     * @param company The company
     * @param folderType The folder type
     * @return The folder
     */
    @Transactional
    public Folder findByCompanyAndFolderType(Company company, FolderType folderType) {
        return folderRepository
                .findByCompany_IdAndFolderType(company.getId(), folderType)
                .orElse(null);
    }

    /**
     * Find a folder by user and folder type
     *
     * @param user The user
     * @param folderType The folder type
     * @return The folder
     */
    @Transactional
    public Folder findByUserAndFolderType(User user, FolderType folderType) {
        return folderRepository.findByUser_IdAndFolderType(user.getId(), folderType).orElse(null);
    }
}
