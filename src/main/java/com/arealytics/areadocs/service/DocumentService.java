package com.arealytics.areadocs.service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.arealytics.areadocs.domain.*;
import com.arealytics.areadocs.dto.requestDTO.DocumentFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentUpdateDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.DocumentNotFoundException;
import com.arealytics.areadocs.exception.DocumentTitleConflictException;
import com.arealytics.areadocs.exception.UnauthorizedException;
import com.arealytics.areadocs.mapper.DocumentMapper;
import com.arealytics.areadocs.repository.DocumentRepository;
import com.arealytics.areadocs.repository.FolderRepository;
import com.arealytics.areadocs.util.AuthUtils;
import com.arealytics.areadocs.util.S3UrlBuilder;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class DocumentService {
    private final DocumentRepository documentRepository;
    private final DocumentMapper documentMapper;
    private final FolderRepository folderRepository;
    private final S3Service s3Service;
    private final RestTemplate restTemplate;
    private final AuthUtils authUtils;
    private final S3UrlBuilder s3UrlBuilder;
    private final CompanyMembershipService companyMembershipService;

    @Transactional
    public DocumentDTO updateDocument(Long id, DocumentUpdateDTO requestDTO) {
        Document doc =
                documentRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new DocumentNotFoundException(
                                                "Document not found with id: " + id));

        // Use the validator
        validateOwnerAccess(doc);

        boolean titleChanged =
                StringUtils.hasText(requestDTO.getTitle())
                        && !requestDTO.getTitle().equals(doc.getTitle());
        boolean folderChanged =
                requestDTO.getFolderId() != null
                        && !requestDTO.getFolderId().equals(doc.getFolder().getId());

        if (folderChanged) {
            Folder newFolder =
                    folderRepository
                            .findById(requestDTO.getFolderId())
                            .orElseThrow(
                                    () ->
                                            new DocumentNotFoundException(
                                                    "Destination folder not found with id: "
                                                            + requestDTO.getFolderId()));
            validateFolderAccess(newFolder);
            validateTitleUniqueness(
                    doc.getTitle(), newFolder.getId(), null); // Check old title in new folder
            doc.setFolder(newFolder);
        }

        if (titleChanged) {
            validateTitleUniqueness(requestDTO.getTitle(), doc.getFolder().getId(), doc.getId());
            doc.setTitle(requestDTO.getTitle());
        }

        if (requestDTO.getDescription() != null) {
            doc.setDescription(requestDTO.getDescription());
        }

        Document updatedDocument = documentRepository.save(doc);
        DocumentDTO dto = documentMapper.toDTO(updatedDocument);
        dto.setFilePath(s3UrlBuilder.buildS3Url(dto.getFilePath()));
        return dto;
    }

    @Transactional
    public void deleteDocument(Long id) {
        Document document =
                documentRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new DocumentNotFoundException(
                                                "Document not found with id: " + id));

        // First, validate if the current user can delete this document
        validateOwnerAccess(document);

        documentRepository.deleteById(id);
    }

    /**
     * Get companies with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of company DTOs
     */
    @Transactional(readOnly = true)
    public Page<DocumentDTO> getDocumentsWithFilters(DocumentFilterDTO filter, Pageable pageable) {
        User currentUser = authUtils.getCurrentUser();
        Specification<Document> spec =
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    // Authorization filter based on user type
                    boolean isPlatformAdmin =
                            currentUser.getUserRoles().stream()
                                    .anyMatch(
                                            userRole ->
                                                    "PLATFORM_ADMIN"
                                                            .equals(userRole.getRole().getName()));

                    if (!isPlatformAdmin) {
                        if (currentUser.getUserType() == UserType.INDIVIDUAL) {
                            // Individual users can only see their own documents
                            predicates.add(
                                    cb.equal(root.get("user").get("id"), currentUser.getId()));
                        } else if (currentUser.getUserType() == UserType.COMPANY) {
                            Company currentUserCompany = authUtils.getCurrentUserCompany();
                            if (currentUserCompany != null) {
                                // Company users see documents of all users in their company and
                                // company-owned documents
                                // Predicate for documents owned by users within the company
                                Join<Document, User> userJoin = root.join("user", JoinType.INNER);
                                Join<User, CompanyMembership> membershipJoin =
                                        userJoin.join("companyMemberships", JoinType.INNER);
                                Predicate userDocsInCompany =
                                        cb.equal(
                                                membershipJoin.get("company").get("id"),
                                                currentUserCompany.getId());

                                // Predicate for documents directly owned by the company
                                Predicate companyOwnedDocs =
                                        cb.equal(
                                                root.get("company").get("id"),
                                                currentUserCompany.getId());

                                predicates.add(cb.or(userDocsInCompany, companyOwnedDocs));
                            } else {
                                // Company user with no company sees nothing
                                return cb.disjunction();
                            }
                        }
                    }

                    if (filter != null) {
                        if (filter.getDocumentId() != null) {
                            predicates.add(cb.equal(root.get("id"), filter.getDocumentId()));
                        }

                        if (StringUtils.hasText(filter.getTitle())) {
                            predicates.add(
                                    cb.like(
                                            cb.lower(root.get("title")),
                                            "%" + filter.getTitle().toLowerCase() + "%"));
                        }

                        if (StringUtils.hasText(filter.getDescription())) {
                            predicates.add(
                                    cb.like(
                                            cb.lower(root.get("description")),
                                            "%" + filter.getDescription().toLowerCase() + "%"));
                        }

                        if (filter.getExternalId() != null) {
                            predicates.add(
                                    cb.equal(root.get("externalId"), filter.getExternalId()));
                        }

                        if (filter.getFolderId() != null) {
                            predicates.add(
                                    cb.equal(root.get("folder").get("id"), filter.getFolderId()));
                        }

                        if (filter.getSourceId() != null) {
                            predicates.add(
                                    cb.equal(root.get("source").get("id"), filter.getSourceId()));
                        }

                        if (filter.getCategoryId() != null) {
                            predicates.add(
                                    cb.equal(
                                            root.get("category").get("id"),
                                            filter.getCategoryId()));
                        }

                        if (StringUtils.hasText(filter.getDocumentType())) {
                            predicates.add(
                                    cb.equal(
                                            cb.lower(root.get("documentType")),
                                            filter.getDocumentType().toLowerCase()));
                        }

                        if (filter.getMinBasePrice() != null) {
                            predicates.add(
                                    cb.greaterThanOrEqualTo(
                                            root.get("basePrice"), filter.getMinBasePrice()));
                        }

                        if (filter.getMaxBasePrice() != null) {
                            predicates.add(
                                    cb.lessThanOrEqualTo(
                                            root.get("basePrice"), filter.getMaxBasePrice()));
                        }

                        if (filter.getMinFinalPrice() != null) {
                            predicates.add(
                                    cb.greaterThanOrEqualTo(
                                            root.get("finalPrice"), filter.getMinFinalPrice()));
                        }

                        if (filter.getMaxFinalPrice() != null) {
                            predicates.add(
                                    cb.lessThanOrEqualTo(
                                            root.get("finalPrice"), filter.getMaxFinalPrice()));
                        }

                        if (filter.getMinAccessCount() != null) {
                            predicates.add(
                                    cb.greaterThanOrEqualTo(
                                            root.get("accessCount"), filter.getMinAccessCount()));
                        }

                        if (filter.getMaxAccessCount() != null) {
                            predicates.add(
                                    cb.lessThanOrEqualTo(
                                            root.get("accessCount"), filter.getMaxAccessCount()));
                        }
                        if (Boolean.TRUE.equals(filter.getCreatedInLastDay())) {
                            LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
                            predicates.add(
                                    cb.greaterThanOrEqualTo(root.get("createdAt"), oneDayAgo));
                        }
                    }

                    return cb.and(predicates.toArray(new Predicate[0]));
                };

        Page<DocumentDTO> dtoPage =
                documentRepository.findAll(spec, pageable).map(documentMapper::toDTO);

        // Iterate over the results to build the full URL for each document
        dtoPage.forEach(dto -> dto.setFilePath(s3UrlBuilder.buildS3Url(dto.getFilePath())));

        return dtoPage;
    }

    @Transactional
    public DocumentDTO moveDocument(Long documentId, Long newFolderId) {
        Document document =
                documentRepository
                        .findById(documentId)
                        .orElseThrow(
                                () ->
                                        new DocumentNotFoundException(
                                                "Document not found with id: " + documentId));
        Folder newFolder =
                folderRepository
                        .findById(newFolderId)
                        .orElseThrow(
                                () ->
                                        new DocumentNotFoundException(
                                                "Destination folder not found with id: "
                                                        + newFolderId));

        // Use the validator for all checks
        validateOwnerAccess(document);
        validateFolderAccess(newFolder);
        validateTitleUniqueness(document.getTitle(), newFolder.getId(), null);

        document.setFolder(newFolder);

        Document movedDocument = documentRepository.save(document);
        DocumentDTO dto = documentMapper.toDTO(movedDocument);
        dto.setFilePath(s3UrlBuilder.buildS3Url(dto.getFilePath()));
        return dto;
    }

    public String streamAndUploadToS3(String fileUrl, String s3Folder) {
        ResponseEntity<Resource> response =
                restTemplate.exchange(
                        RequestEntity.get(URI.create(fileUrl)).build(), Resource.class);

        if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
            throw new RuntimeException("Failed to fetch file stream");
        }

        String contentType =
                response.getHeaders().getContentType() != null
                        ? response.getHeaders().getContentType().toString()
                        : "application/octet-stream";

        long contentLength = response.getHeaders().getContentLength();
        String fileName = UUID.randomUUID() + ".pdf";
        String key = s3Folder.endsWith("/") ? s3Folder + fileName : s3Folder + "/" + fileName;

        try (InputStream inputStream = response.getBody().getInputStream()) {
            s3Service.uploadStream(inputStream, contentLength, key, contentType);
        } catch (IOException e) {
            throw new RuntimeException("Error reading file stream", e);
        }

        return key;
    }

    /* ****************************************
     * ************* Validations **************
     * ****************************************/

    /**
     * Validates if the current user can access a specific folder.
     *
     * @param folder The folder to check.
     * @throws UnauthorizedException if access is denied.
     */
    private void validateFolderAccess(Folder folder) {
        User currentUser = authUtils.getCurrentUser();

        boolean isPlatformAdmin =
                currentUser.getUserRoles().stream()
                        .anyMatch(
                                userRole -> "PLATFORM_ADMIN".equals(userRole.getRole().getName()));
        if (isPlatformAdmin) {
            return; // Platform admin can access any folder
        }

        if (currentUser.getUserType() == UserType.INDIVIDUAL) {
            if (folder.getUser() == null || !folder.getUser().getId().equals(currentUser.getId())) {
                throw new UnauthorizedException(
                        "You do not have permission to access the destination folder.");
            }
        } else if (currentUser.getUserType() == UserType.COMPANY) {
            Company currentUserCompany = authUtils.getCurrentUserCompany();
            if (folder.getCompany() == null
                    || !folder.getCompany().getId().equals(currentUserCompany.getId())) {
                throw new UnauthorizedException(
                        "The destination folder does not belong to your company.");
            }
        }
    }

    /**
     * Validates if a document's title is unique within the given folder.
     *
     * @param title The title to check.
     * @param folderId The ID of the folder to check within.
     * @param documentIdToExclude The ID of the document to exclude from the check (used for
     *     updates).
     * @throws DocumentTitleConflictException if the title is not unique.
     */
    private void validateTitleUniqueness(String title, Long folderId, Long documentIdToExclude) {
        boolean conflict;
        if (documentIdToExclude != null) {
            // This is for an update on an existing document
            conflict =
                    documentRepository.existsByFolderIdAndTitleAndIdNot(
                            folderId, title, documentIdToExclude);
        } else {
            // This is for a new document or a move operation
            conflict = documentRepository.existsByFolderIdAndTitle(folderId, title);
        }

        if (conflict) {
            throw new DocumentTitleConflictException(
                    "A document with title '" + title + "' already exists in this folder.");
        }
    }

    /**
     * Validates if the current user has permission to access the owner of a given document.
     *
     * @param document The document to check access for.
     * @throws UnauthorizedException if access is denied.
     */
    public void validateOwnerAccess(Document document) {
        User currentUser = authUtils.getCurrentUser();
        if (currentUser == null) {
            throw new UnauthorizedException("User not authenticated.");
        }

        boolean isPlatformAdmin =
                currentUser.getUserRoles().stream()
                        .anyMatch(
                                userRole -> "PLATFORM_ADMIN".equals(userRole.getRole().getName()));
        if (isPlatformAdmin) {
            return;
        }

        User documentOwner = document.getUser();
        Company documentCompany = document.getCompany(); // Get directly associated company

        if (documentOwner == null && documentCompany == null) {
            throw new UnauthorizedException(
                    "Document has no owner or associated company, access denied.");
        }

        if (currentUser.getUserType() == UserType.INDIVIDUAL) {
            // Individual user must own the document directly
            if (documentOwner == null || !documentOwner.getId().equals(currentUser.getId())) {
                throw new UnauthorizedException(
                        "You do not have permission to access this document.");
            }
        } else if (currentUser.getUserType() == UserType.COMPANY) {
            Company currentUserCompany = authUtils.getCurrentUserCompany();
            if (currentUserCompany == null) {
                throw new UnauthorizedException(
                        "Your user account is not associated with a company.");
            }

            boolean hasAccessViaUserMembership = false;
            if (documentOwner != null) {
                // Fetch company membership for the document owner and check if it matches the
                // current user's company
                CompanyMembershipDTO membership = // Changed type to CompanyMembershipDTO
                        companyMembershipService.getCompanyMembershipsByUserId(
                                documentOwner.getId());
                if (membership != null
                        && membership.getCompanyId() != null // Access companyId from DTO
                        && membership
                                .getCompanyId()
                                .equals(currentUserCompany.getId())) { // Compare using getId()
                    hasAccessViaUserMembership = true;
                }
            }

            boolean hasAccessViaDirectCompanyAssociation = false;
            if (documentCompany != null
                    && documentCompany.getId().equals(currentUserCompany.getId())) {
                hasAccessViaDirectCompanyAssociation = true;
            }

            if (!(hasAccessViaUserMembership || hasAccessViaDirectCompanyAssociation)) {
                throw new UnauthorizedException(
                        "You do not have permission to access this document.");
            }
        }
    }
}
