package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.dto.requestDTO.CompanySpecialPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanySpecialPriceResponseDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.CompanySpecialPriceMapper;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.CompanySpecialPriceRepository;

import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanySpecialPriceService {

    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final CompanyRepository companyRepository;
    private final CompanySpecialPriceMapper companySpecialPriceMapper;
    private final MockServApiService mockServApiService;

    @Transactional
    public CompanySpecialPriceResponseDTO updateCompanySpecialPrice(
            Long id, CompanySpecialPriceRequestDTO requestDTO) {
        CompanySpecialPrice existingCompanySpecialPrice =
                companySpecialPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company special price not found with id: " + id));

        if (requestDTO.getCompanyId() == null) {
            throw new IllegalArgumentException("company id must be provided");
        }
    
        // Prevent changing companyId to avoid unique constraint violations
        Long requestedCompanyId = requestDTO.getCompanyId();
        if (!requestedCompanyId.equals(existingCompanySpecialPrice.getCompany().getId())) {
            throw new CompanyException(
                    "Cannot change company ID for company special price with id: " + id +
                    ". Current company ID: " + existingCompanySpecialPrice.getCompany().getId() +
                    ", Requested company ID: " + requestedCompanyId);
        }

        DocumentPrice documentPrice = existingCompanySpecialPrice.getDocumentPrice();
        BigDecimal gst = requestDTO.getSpecialPrice() != null
                ? mockServApiService.calculateGst(requestDTO.getSpecialPrice(), existingCompanySpecialPrice.getSpecialPriceGst())
                : documentPrice.getEffectivePriceGst();
        existingCompanySpecialPrice.setSpecialPriceGst(gst);
    
        companySpecialPriceMapper.updateCompanySpecialPriceFromDto(requestDTO, existingCompanySpecialPrice);
        setRelationships(existingCompanySpecialPrice, requestDTO);
    
        CompanySpecialPrice saved = companySpecialPriceRepository.saveAndFlush(existingCompanySpecialPrice);
        return companySpecialPriceMapper.toDto(saved);
    }

    @Transactional(readOnly = true)
    public CompanySpecialPriceResponseDTO getCompanySpecialPriceById(Long id) {
        CompanySpecialPrice companySpecialPrice =
                companySpecialPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company special price not found with id: " + id));
        return companySpecialPriceMapper.toDto(companySpecialPrice);
    }

    @Transactional(readOnly = true)
    public Page<CompanySpecialPriceResponseDTO> getCompanySpecialPrices(
            Long companyId, Long documentPriceId, Pageable pageable) {
        Specification<CompanySpecialPrice> spec =
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (companyId != null) {
                        predicates.add(
                                criteriaBuilder.equal(root.get("company").get("id"), companyId));
                    }

                    if (documentPriceId != null) {
                        predicates.add(
                                criteriaBuilder.equal(
                                        root.get("documentPrice").get("id"), documentPriceId));
                    }

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

        return companySpecialPriceRepository
                .findAll(spec, pageable)
                .map(companySpecialPriceMapper::toDto);
    }

    @Transactional
    public void deleteCompanySpecialPrice(Long id) {
        CompanySpecialPrice companySpecialPrice =
                companySpecialPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company special price not found with id: " + id));
        companySpecialPrice.softDelete();
        companySpecialPriceRepository.save(companySpecialPrice);
    }

    private void validateUniqueConstraint(Long companyId, Long documentPriceId) {
        if (companySpecialPriceRepository.existsByCompanyIdAndDocumentPriceId(
                companyId, documentPriceId)) {
            throw new CompanyException(
                    "A company special price already exists for company ID "
                            + companyId
                            + ", document price  ID "
                            + documentPriceId);
        }
    }

    private void setRelationships(
            CompanySpecialPrice companySpecialPrice, CompanySpecialPriceRequestDTO requestDTO) {
        Company company =
                companyRepository
                        .findById(requestDTO.getCompanyId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with id: "
                                                        + requestDTO.getCompanyId()));
        companySpecialPrice.setCompany(company);
    }
}
