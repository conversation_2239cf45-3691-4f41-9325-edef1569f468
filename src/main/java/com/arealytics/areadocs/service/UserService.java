package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.*;
import com.arealytics.areadocs.dto.requestDTO.CompanyMembershipRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserSchemaFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.dto.responseDTO.UserResponseDTO;
import com.arealytics.areadocs.enumeration.AddressType;
import com.arealytics.areadocs.enumeration.DeactivationReason;
import com.arealytics.areadocs.enumeration.FolderType;
import com.arealytics.areadocs.enumeration.PasswordActionType;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.exception.UserUpdateException;
import com.arealytics.areadocs.mapper.AddressMapper;
import com.arealytics.areadocs.mapper.UserMapper;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.RoleRepository;
import com.arealytics.areadocs.repository.StatesRepository;
import com.arealytics.areadocs.repository.UserRepository;
import com.arealytics.areadocs.repository.UserRoleRepository;
import com.arealytics.areadocs.repository.ZipCodesRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final KeycloakUserService keycloakUserService;
    private final AccountService accountService;
    private final CompanyMembershipService companyMembershipService;
    private final AddressMapper addressMapper;
    private final RoleRepository roleRepository;
    private final UserRoleRepository userRoleRepository;
    private final CompanyMembershipRepository companyMembershipRepository;
    private final S3Service s3Service;
    private final StatesRepository statesRepository;
    private final ZipCodesRepository zipCodesRepository;
    private final CompanyRepository companyRepository;

    @Value("${temp-profile-images-path}")
    private String tempProfileImagesRelativePath;

    private final FolderService folderService;

    /**
     * Create a new user
     *
     * @param userDTO The user to create
     * @return The created user DTO
     */
    @Transactional
    public UserResponseDTO createUser(UserRequestDTO userDTO, String profilePictureUrl) {
        // 1. Validate user request
        if (userRepository.existsByEmail(userDTO.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + userDTO.getEmail());
        }

        if (userDTO.getUserType() == UserType.COMPANY
                && (userDTO.getCompanyId() == null || userDTO.getCompanyId() <= 0)) {
            throw new IllegalArgumentException("Company ID is required for COMPANY type users");
        }
        // Check company active status
        if (userDTO.getUserType() == UserType.COMPANY) {
            Company company =
                    companyRepository
                            .findById(userDTO.getCompanyId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "Company not found with ID: "
                                                            + userDTO.getCompanyId()));
            if (!company.getIsActive()) {
                log.error(
                        "Cannot create user for deactivated company ID: {}",
                        userDTO.getCompanyId());
                throw new CompanyException(
                        "Cannot create user for deactivated company: " + company.getName());
            }
        }

        if (userDTO.getUserType() == UserType.INDIVIDUAL && userDTO.getCompanyId() != null) {
            throw new IllegalArgumentException(
                    "Company ID should not be provided for INDIVIDUAL type users");
        }

        // 2. Build user entity
        User user = userMapper.toEntity(userDTO);
        user.setProfilePictureUrl(profilePictureUrl);

        // 3. Handle addresses
        if (userDTO.getPrimaryAddress() != null) {
            Address primaryAddress = addressMapper.toEntity(userDTO.getPrimaryAddress());
            primaryAddress.setAddressType(AddressType.PRIMARY);
            States state =
                    statesRepository
                            .findById(userDTO.getPrimaryAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + userDTO.getPrimaryAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(userDTO.getPrimaryAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + userDTO.getPrimaryAddress()
                                                                    .getZipCodeId()));
            primaryAddress.setState(state);
            primaryAddress.setZipCode(zipCode);
            user.setPrimaryAddress(primaryAddress);

            // Handle billing address
            if (Boolean.TRUE.equals(userDTO.getIsBillingPrimary())) {
                user.setBillingAddress(primaryAddress);
            } else if (userDTO.getBillingAddress() != null) {
                Address billingAddress = addressMapper.toEntity(userDTO.getBillingAddress());
                billingAddress.setAddressType(AddressType.BILLING);
                state =
                        statesRepository
                                .findById(userDTO.getBillingAddress().getStateId())
                                .orElseThrow(
                                        () ->
                                                new EntityNotFoundException(
                                                        "State not found with id: "
                                                                + userDTO.getBillingAddress()
                                                                        .getStateId()));
                zipCode =
                        zipCodesRepository
                                .findById(userDTO.getBillingAddress().getZipCodeId())
                                .orElseThrow(
                                        () ->
                                                new EntityNotFoundException(
                                                        "ZipCode not found with id: "
                                                                + userDTO.getBillingAddress()
                                                                        .getZipCodeId()));
                billingAddress.setState(state);
                billingAddress.setZipCode(zipCode);
                user.setBillingAddress(billingAddress);
            } else {
                throw new IllegalArgumentException(
                        "Billing address is required when isBillingPrimary is false");
            }
        } else {
            throw new IllegalArgumentException("Primary address is required");
        }

        // 4. Create user in Keycloak and database
        User savedUser;
        try {
            String keycloakUserId = keycloakUserService.createUser(userDTO);
            log.info("User created in Keycloak with ID: {}", keycloakUserId);

            user.setKeycloakId(keycloakUserId);
            savedUser = userRepository.save(user);
            log.info("User created in database with ID: {}", savedUser.getId());

        } catch (Exception e) {
            log.error("Failed to create user: {}", e.getMessage(), e);

            // Cleanup Keycloak user if needed
            if (user.getKeycloakId() != null) {
                try {
                    keycloakUserService.deleteUser(user);
                    log.info("Cleaned up Keycloak user after failure");
                } catch (Exception ex) {
                    log.error("Failed to clean up Keycloak user: {}", ex.getMessage());
                }
            }
            throw new RuntimeException("Failed to create user: " + e.getMessage(), e);
        }

        // 5. Assign roles and create company membership
        UserRole userRole = null;
        Long roleId = userDTO.getRoleId();
        try {
            if (savedUser.getUserType() == UserType.INDIVIDUAL) {
                userRole = assignRole(savedUser, roleId, null);

            } else if (savedUser.getUserType() == UserType.COMPANY) {
                Long companyId = userDTO.getCompanyId();

                userRole = assignRole(savedUser, roleId, companyId);

                CompanyMembershipRequestDTO membershipDTO = new CompanyMembershipRequestDTO();
                membershipDTO.setUserId(savedUser.getId());
                membershipDTO.setCompanyId(companyId);
                membershipDTO.setRoleId(roleId);
                companyMembershipService.createCompanyMembership(membershipDTO);
            }

        } catch (Exception e) {
            log.error("Failed to assign roles/membership: {}", e.getMessage(), e);

            // Cleanup user and Keycloak data
            try {
                keycloakUserService.deleteUser(savedUser);
                userRepository.delete(savedUser);
                log.info("Cleaned up user after role/membership assignment failure");
            } catch (Exception ex) {
                log.error(
                        "Failed to clean up user after role/membership assignment failure: {}",
                        ex.getMessage());
            }
            throw new RuntimeException("Failed to complete user setup: " + e.getMessage(), e);
        }

        // 6. Create an account for non-company users and create the default folder
        if (savedUser.getUserType() != UserType.COMPANY) {
            log.info("Creating account for new user with ID: {}", savedUser.getId());
            accountService.createUserAccount(savedUser);
            log.info("Creating default folders for new user with ID: {}", savedUser.getId());
            Folder rootFolder =
                    folderService.createSystemFolder(savedUser, null, FolderType.ROOT, null);
        } else {
            log.info(
                    "Skipping account creation for COMPANY type user with ID: {}",
                    savedUser.getId());
        }

        // 7. Build and return response
        UserResponseDTO userResponseDTO = userMapper.toDto(savedUser);
        if (userDTO.getUserType() == UserType.COMPANY) {
            userResponseDTO.setCompanyId(userDTO.getCompanyId());
        }
        if (userRole != null) {
            userResponseDTO.setRoleId(userRole.getRole().getId());
            userResponseDTO.setRoleName(userRole.getRole().getName());
            userResponseDTO.setRoleDisplayText(userRole.getRole().getDisplayText());
        }

        return userResponseDTO;
    }

    /**
     * Send a password update email to a user (for initial setup or password reset).
     *
     * @param identifier The user's email or Keycloak ID
     * @param isEmail True if the identifier is an email, false if it's a Keycloak ID
     * @param actionType The type of action (e.g., "Initial Setup" or "Password Reset")
     */
    public void sendPasswordUpdateEmail(
            String identifier, boolean isEmail, PasswordActionType actionType) {
        try {
            String keycloakId =
                    isEmail ? keycloakUserService.getUserIdByEmail(identifier) : identifier;
            keycloakUserService.sendUpdatePasswordEmail(keycloakId, actionType);
            log.info("{} email sent for identifier: {}", actionType.getDisplayName(), identifier);
        } catch (Exception e) {
            log.error(
                    "Failed to send {} email for identifier {}: {}",
                    actionType.getDisplayName(),
                    identifier,
                    e.getMessage(),
                    e);
            throw new RuntimeException(
                    "Failed to send "
                            + actionType.getDisplayName().toLowerCase()
                            + " email: "
                            + e.getMessage(),
                    e);
        }
    }

    /**
     * Get all users
     *
     * @return List of all user DTOs
     */
    @Transactional(readOnly = true)
    public Page<UserResponseDTO> getAllUsers(Pageable pageable) {
        Page<User> userPage = userRepository.findAll(pageable);
        List<UserResponseDTO> userDtos =
                userPage.getContent().stream()
                        .map(userMapper::toDto)
                        .peek(this::setCompanyAndRoleDetails) // Updated to use single method
                        .collect(Collectors.toList());
        return new PageImpl<>(userDtos, pageable, userPage.getTotalElements());
    }

    /**
     * Get a user by ID
     *
     * @param id The user ID
     * @return The user DTO
     */
    @Transactional(readOnly = true)
    public UserResponseDTO getUserById(Long id) {
        User user =
                userRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new EntityNotFoundException("User not found with id: " + id));

        // 1. Convert to DTO
        UserResponseDTO userDto = userMapper.toDto(user);
        setCompanyAndRoleDetails(userDto);
        return userDto;
    }

    /**
     * Get a user by email
     *
     * @param email The user email
     * @return The user DTO
     */
    @Transactional(readOnly = true)
    public UserResponseDTO getUserByEmail(String email) {
        User user =
                userRepository
                        .findByEmail(email)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User not found with email: " + email));
        // 1 Convert to DTO
        UserResponseDTO userDto = userMapper.toDto(user);
        setCompanyAndRoleDetails(userDto);
        return userDto;
    }

    /**
     * Update a user
     *
     * @param id The user ID
     * @param userRequestDTO The updated user data
     * @return The updated user DTO
     */
    @Transactional
    public UserResponseDTO updateUser(
            Long id, UserRequestDTO userRequestDTO, String profilePictureUrl) {
        // 1. Validate and fetch existing user
        User existingUser =
                userRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new EntityNotFoundException("User not found with id: " + id));
        System.out.println(existingUser.getEmail());
        System.out.println(userRequestDTO.getEmail());
        // 2. Validate email change
        if (!existingUser.getEmail().equals(userRequestDTO.getEmail())
                && userRepository.existsByEmail(userRequestDTO.getEmail())) {
            throw new IllegalArgumentException(
                    "Email already exists: " + userRequestDTO.getEmail());
        }

        // 3. Validate company ID for COMPANY users (if user type is being changed or
        // company ID is
        // provided)
        if (userRequestDTO.getUserType() == UserType.COMPANY
                && (userRequestDTO.getCompanyId() == null || userRequestDTO.getCompanyId() <= 0)) {
            throw new IllegalArgumentException("Company ID is required for COMPANY type users");
        }

        // 4. Update basic user fields
        existingUser.setEmail(userRequestDTO.getEmail());
        existingUser.setFirstName(userRequestDTO.getFirstName());
        existingUser.setMiddleName(userRequestDTO.getMiddleName());
        existingUser.setLastName(userRequestDTO.getLastName());
        existingUser.setContactNumber(userRequestDTO.getContactNumber());
        existingUser.setIsActive(userRequestDTO.getIsActive());
        existingUser.setBio(userRequestDTO.getBio());

        boolean removeProfile = Boolean.TRUE.equals(userRequestDTO.getRemoveProfilePicture());
        if (removeProfile) {
            existingUser.setProfilePictureUrl(null);
        } else if (profilePictureUrl != null && !profilePictureUrl.isEmpty()) {
            existingUser.setProfilePictureUrl(profilePictureUrl);
        }

        // Update user type
        if (userRequestDTO.getUserType() != null) {
            existingUser.setUserType(userRequestDTO.getUserType());
        }

        // 5. Handle primary address
        if (userRequestDTO.getPrimaryAddress() != null) {
            Address primaryAddress = existingUser.getPrimaryAddress();
            if (primaryAddress == null) {
                primaryAddress = new Address();
                primaryAddress.setAddressType(AddressType.PRIMARY);
            }
            addressMapper.updateAddressFromDTO(userRequestDTO.getPrimaryAddress(), primaryAddress);
            States state =
                    statesRepository
                            .findById(userRequestDTO.getPrimaryAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + userRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(userRequestDTO.getPrimaryAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + userRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getZipCodeId()));
            primaryAddress.setState(state);
            primaryAddress.setZipCode(zipCode);
            existingUser.setPrimaryAddress(primaryAddress);
        }

        // 6. Handle billing address
        if (Boolean.TRUE.equals(userRequestDTO.getIsBillingPrimary())) {
            if (existingUser.getPrimaryAddress() == null) {
                throw new IllegalArgumentException(
                        "Cannot use primary address as billing address when primary address is not"
                                + " set");
            }
            existingUser.setBillingAddress(existingUser.getPrimaryAddress());
        } else if (userRequestDTO.getBillingAddress() != null) {
            Address billingAddress = existingUser.getBillingAddress();

            // Create new billing address if none exists or if currently using primary as
            // billing
            if (billingAddress == null || billingAddress.equals(existingUser.getPrimaryAddress())) {
                billingAddress = new Address();
                billingAddress.setAddressType(AddressType.BILLING);
            }
            addressMapper.updateAddressFromDTO(userRequestDTO.getBillingAddress(), billingAddress);
            States state =
                    statesRepository
                            .findById(userRequestDTO.getBillingAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + userRequestDTO
                                                                    .getBillingAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(userRequestDTO.getBillingAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + userRequestDTO
                                                                    .getBillingAddress()
                                                                    .getZipCodeId()));
            billingAddress.setState(state);
            billingAddress.setZipCode(zipCode);
            existingUser.setBillingAddress(billingAddress);
        } else {
            throw new IllegalArgumentException(
                    "Billing address is required when isBillingPrimary is false");
        }

        // 7. Handle role updates for company users
        UserRole updatedRole = null;
        // Replace the role update logic with:
        if (existingUser.getUserType() == UserType.COMPANY && userRequestDTO.getRoleId() != null) {
            try {
                // Update company membership role if role ID is provided
                CompanyMembership existingMembership =
                        companyMembershipRepository
                                .findByUserIdAndCompanyId(
                                        existingUser.getId(), userRequestDTO.getCompanyId())
                                .orElseThrow(
                                        () ->
                                                new EntityNotFoundException(
                                                        "Company membership not found for user: "
                                                                + existingUser.getId()));
                CompanyMembershipRequestDTO membershipDTO = new CompanyMembershipRequestDTO();
                membershipDTO.setUserId(existingUser.getId());
                membershipDTO.setCompanyId(userRequestDTO.getCompanyId());
                membershipDTO.setRoleId(userRequestDTO.getRoleId());
                companyMembershipService.updateCompanyMembership(
                        existingMembership.getId(), membershipDTO);
                // Update user role
                updatedRole =
                        assignRole(
                                existingUser,
                                userRequestDTO.getRoleId(),
                                userRequestDTO.getCompanyId());

            } catch (Exception e) {
                log.error("Failed to update user role/membership: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to update user role: " + e.getMessage(), e);
            }
        }

        // 8. Save updated user
        User savedUser = userRepository.save(existingUser);
        log.info("User updated successfully with ID: {}", savedUser.getId());

        // 9. Update user details in Keycloak
        try {
            keycloakUserService.updateUserDetails(savedUser.getKeycloakId(), userRequestDTO);
            log.info(
                    "User details updated successfully in Keycloak for ID: {}",
                    savedUser.getKeycloakId());
        } catch (Exception e) {
            log.error("Failed to update user details in Keycloak: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update user details in Keycloak", e);
        }

        // 10. Build and return response
        UserResponseDTO response = userMapper.toDto(savedUser);
        if (existingUser.getUserType() == UserType.COMPANY
                && userRequestDTO.getCompanyId() != null) {
            response.setCompanyId(userRequestDTO.getCompanyId());
        }
        if (updatedRole != null) {
            response.setRoleId(updatedRole.getRole().getId());
            response.setRoleName(updatedRole.getRole().getName());
            response.setRoleDisplayText(updatedRole.getRole().getDisplayText());
        } else {
            setCompanyAndRoleDetails(response);
        }

        return response;
    }

    /**
     * Delete a user
     *
     * @param id The user ID
     */
    @Transactional
    public void deleteUser(Long id) {
        log.info("Deleting user with ID: {}", id);

        // Verify user exists
        User user =
                userRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new EntityNotFoundException("User not found with id: " + id));

        // Delegate deletion to KeycloakUserService
        keycloakUserService.deleteUser(user);

        user.softDelete();
        userRepository.save(user);
        log.info("User status updated to INACTIVE in database with ID: {}", id);

        log.info("User deletion completed for ID: {}", id);
    }

    /**
     * Search for users based on filter criteria
     *
     * @param filterDTO The filter criteria
     * @param pageable Pagination information
     * @return Page of user DTOs
     */
    @Transactional(readOnly = true)
    public Page<UserResponseDTO> getUsers(UserSchemaFilterDTO filterDTO, Pageable pageable) {
        Page<User> userPage;

        if (filterDTO.getUserType() != null) {
            userPage = userRepository.findByUserType(filterDTO.getUserType(), pageable);
        } else {
            userPage = userRepository.findAll(pageable);
        }
        // 2. Convert to DTOs
        List<UserResponseDTO> userDtos =
                userPage.getContent().stream()
                        .map(userMapper::toDto)
                        .peek(this::setCompanyAndRoleDetails)
                        .collect(Collectors.toList());
        return new PageImpl<>(userDtos, pageable, userPage.getTotalElements());
    }

    /**
     * Get users by company ID
     *
     * @param companyId The company ID
     * @param pageable Pagination information
     * @return Page of user DTOs
     */
    @Transactional(readOnly = true)
    public Page<UserResponseDTO> getUsersByCompanyId(Long companyId, Pageable pageable) {
        Page<User> userPage =
                userRepository
                        .findByCompanyMemberships_Company_IdAndCompanyMemberships_IsActiveTrue(
                                companyId, pageable);
        List<UserResponseDTO> userDtos =
                userPage.getContent().stream()
                        .map(userMapper::toDto)
                        .peek(this::setCompanyAndRoleDetails)
                        .collect(Collectors.toList());
        return new PageImpl<>(userDtos, pageable, userPage.getTotalElements());
    }

    /**
     * Assign a default role to a user
     *
     * @param user The user to assign the role to
     * @param roleId The ID of the role to assign
     * @return The created user role
     */
    private UserRole assignRole(User user, Long roleId, Long companyId) {
        Role role =
                roleRepository
                        .findById(roleId)
                        .orElseThrow(
                                () -> new EntityNotFoundException("Role not found: " + roleId));
        // check company admin role already assigned to any other user if exists then
        // throw
        // exception
        if (role.getName().equals("COMPANY_ADMIN") && companyId != null) {
            if (companyMembershipRepository.existsByCompanyIdAndRoleNameAndIsActiveTrue(
                    companyId, "COMPANY_ADMIN")) {
                throw new RoleException("Company admin role already assigned to another user");
            }
        }

        // Check if user already has this specific role
        UserRole existingUserRole = userRoleRepository.findByUserId(user.getId());

        if (existingUserRole != null && !existingUserRole.getRole().getId().equals(roleId)) {
            existingUserRole.setIsActive(true);
            existingUserRole.setModifiedAt(Instant.now());
            existingUserRole.setVersion(existingUserRole.getVersion() + 1);
            existingUserRole.setRole(role);
            log.info("Reactivated role '{}' for user '{}'", role.getName(), user.getEmail());
            return userRoleRepository.save(existingUserRole);
        } else if (existingUserRole != null) {
            log.info("User '{}' already has role '{}'", user.getEmail(), role.getName());
            return existingUserRole;
        } else {
            UserRole userRole = new UserRole();
            userRole.setUser(user);
            userRole.setRole(role);
            userRole.setIsActive(true);
            userRole.setModifiedAt(Instant.now());
            userRole.setVersion(0L);
            log.info("Assigned role '{}' to user '{}'", role.getName(), user.getEmail());
            return userRoleRepository.save(userRole);
        }
    }

    private void setCompanyAndRoleDetails(
            UserResponseDTO dto, Map<Long, CompanyMembershipDTO> membershipMap) {
        if (UserType.COMPANY.name().equals(dto.getUserType())) {
            // Set company details for COMPANY users
            CompanyMembershipDTO membership = membershipMap.get(dto.getId());
            if (membership != null) {
                dto.setCompanyId(membership.getCompanyId());
                dto.setRoleId(membership.getRoleId());
                dto.setRoleName(membership.getRoleName());
                dto.setRoleDisplayText(membership.getRoleDisplayText());
            }
        } else {
            getRoleForIndividualUser(dto);
        }
    }

    /**
     * Get the role for an individual user
     *
     * @param user The user DTO
     */
    private void getRoleForIndividualUser(UserResponseDTO user) {
        UserRole userRole = userRoleRepository.findByUserId(user.getId());
        if (userRole != null && userRole.getRole() != null && userRole.getIsActive()) {
            user.setRoleId(userRole.getRole().getId());
            user.setRoleName(userRole.getRole().getName()); // Set roleName
            user.setRoleDisplayText(userRole.getRole().getDisplayText());
        }
    }

    private void setCompanyAndRoleDetails(UserResponseDTO dto) {
        if (UserType.COMPANY.name().equals(dto.getUserType())) {
            CompanyMembershipDTO membership =
                    companyMembershipService.getCompanyMembershipsByUserId(dto.getId());
            if (membership != null) {
                dto.setCompanyId(membership.getCompanyId());
                dto.setRoleId(membership.getRoleId());
                dto.setRoleName(membership.getRoleName());
                dto.setRoleDisplayText(membership.getRoleDisplayText());
                dto.setACN(membership.getCompanyACN());
                dto.setABN(membership.getCompanyABN());
            }
        } else {
            getRoleForIndividualUser(dto);
        }
    }

    @Transactional
    public UserResponseDTO updateUserActiveStatus(Long id, Boolean isActive) {
        User user =
                userRepository
                        .findById(id)
                        .orElseThrow(
                                () -> {
                                    log.error("User not found with ID: {}", id);
                                    return new EntityNotFoundException(
                                            "User not found with ID: " + id);
                                });

        try {
            user.setIsActive(isActive);
            if (!isActive) {
                user.setDeactivationReason(DeactivationReason.MANUAL);
                user.setDeleteAt(Instant.now());
            } else {
                user.setDeactivationReason(null);
                user.setDeleteAt(null);
            }
            User updatedUser = userRepository.save(user);
            log.info("User active status updated successfully to {} for ID: {}", isActive, id);

            // Update Keycloak enabled status
            keycloakUserService.updateUserEnabledStatus(user.getKeycloakId(), isActive);

            UserResponseDTO response = userMapper.toDto(updatedUser);
            setCompanyAndRoleDetails(response);
            return response;
        } catch (Exception e) {
            log.error("Error updating user active status for ID {}: {}", id, e.getMessage(), e);
            throw new UserUpdateException(e.getMessage(), e);
        }
    }

    public void updateUserEnabledStatus(String keycloakId, boolean enabled) {
        keycloakUserService.updateUserEnabledStatus(keycloakId, enabled);
    }
}
