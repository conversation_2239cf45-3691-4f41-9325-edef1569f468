package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.BulkUserOperationRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.BulkUserOperationResponseDTO;
import com.arealytics.areadocs.enumeration.DeactivationReason;
import com.arealytics.areadocs.repository.UserRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class BulkUserOperationService {

    private final KeycloakUserService keycloakUserService;
    private final UserRepository userRepository;

    /**
     * Perform bulk user operations (enable/disable) This method coordinates between Keycloak and
     * the local database
     *
     * @param request The bulk operation request
     * @return The bulk operation response with results
     */
    @Transactional
    public BulkUserOperationResponseDTO performBulkOperation(BulkUserOperationRequestDTO request) {
        log.info(
                "Starting bulk {} operation for {} users",
                request.getOperation(),
                request.getUsernames().size());

        try {
            // 1. Call Keycloak SPI to perform bulk operation
            Map<String, String> keycloakResults =
                    keycloakUserService.performBulkUserOperation(request);

            // 2. Update local database for successful operations
            updateLocalDatabase(request, keycloakResults);

            // 3. Create and return response
            BulkUserOperationResponseDTO response =
                    new BulkUserOperationResponseDTO(keycloakResults);

            log.info(
                    "Bulk {} operation completed. Total: {}, Success: {}, Failed: {}",
                    request.getOperation(),
                    response.getTotalProcessed(),
                    response.getSuccessCount(),
                    response.getFailureCount());

            return response;

        } catch (Exception e) {
            log.error(
                    "Error performing bulk {} operation: {}",
                    request.getOperation(),
                    e.getMessage(),
                    e);
            throw new RuntimeException("Failed to perform bulk operation: " + e.getMessage(), e);
        }
    }

    /**
     * Update local database based on Keycloak operation results
     *
     * @param request The original request
     * @param keycloakResults Results from Keycloak operation
     */
    private void updateLocalDatabase(
            BulkUserOperationRequestDTO request, Map<String, String> keycloakResults) {
        boolean isEnableOperation = "enable".equalsIgnoreCase(request.getOperation());

        for (Map.Entry<String, String> entry : keycloakResults.entrySet()) {
            String username = entry.getKey();
            String result = entry.getValue();

            // Only update database for successful operations
            if ((isEnableOperation && "enabled".equals(result))
                    || (!isEnableOperation && "disabled".equals(result))) {

                try {
                    updateUserInDatabase(username, isEnableOperation);
                } catch (Exception e) {
                    log.error(
                            "Failed to update local database for user {}: {}",
                            username,
                            e.getMessage());
                    // Note: We don't throw here to avoid rolling back successful Keycloak
                    // operations
                    // The inconsistency should be handled by a separate reconciliation process
                }
            }
        }
    }

    /**
     * Update a single user in the local database
     *
     * @param username The username (email) of the user
     * @param isActive Whether to activate or deactivate the user
     */
    private void updateUserInDatabase(String username, boolean isActive) {
        Optional<User> userOptional = userRepository.findByEmail(username);

        if (userOptional.isEmpty()) {
            log.warn("User not found in local database: {}", username);
            return;
        }

        User user = userOptional.get();
        user.setIsActive(isActive);

        if (!isActive) {
            user.setDeactivationReason(DeactivationReason.MANUAL);
            user.setDeleteAt(Instant.now());
        } else {
            user.setDeactivationReason(null);
            user.setDeleteAt(null);
        }

        userRepository.save(user);
        log.debug("Updated local database for user {}: isActive={}", username, isActive);
    }
}
