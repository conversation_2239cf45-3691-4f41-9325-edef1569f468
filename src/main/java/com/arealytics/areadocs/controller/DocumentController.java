package com.arealytics.areadocs.controller;

import java.math.BigDecimal;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.DocumentFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentUpdateDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;
import com.arealytics.areadocs.service.DocumentService;
import com.arealytics.areadocs.util.Utils;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/documents")
public class DocumentController {

    private final DocumentService documentService;

    // -------------------------- DOCUMENT --------------------------

    @GetMapping("")
    @Operation(
            summary = "Get documents",
            description = "Retrieves documents based on provided filters with pagination")
    public ResponseEntity<ApiResponse<Page<DocumentDTO>>> getDocuments(
            @RequestParam(required = false) Long documentId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long externalId,
            @RequestParam(required = false) Long folderId,
            @RequestParam(required = false) Long sourceId,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String documentType,
            @RequestParam(required = false) BigDecimal minBasePrice,
            @RequestParam(required = false) BigDecimal maxBasePrice,
            @RequestParam(required = false) BigDecimal minFinalPrice,
            @RequestParam(required = false) BigDecimal maxFinalPrice,
            @RequestParam(required = false) Integer minAccessCount,
            @RequestParam(required = false) Integer maxAccessCount,
            @RequestParam(required = false) Boolean createdInLastDay,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {

        Pageable pageable = PageRequest.of(page, size, Utils.parseSort(sort));

        // Populate all filter criteria into the DTO
        DocumentFilterDTO filter = new DocumentFilterDTO();
        filter.setDocumentId(documentId);
        filter.setTitle(title);
        filter.setDescription(description);
        filter.setExternalId(externalId);
        filter.setFolderId(folderId);
        filter.setSourceId(sourceId);
        filter.setCategoryId(categoryId);
        filter.setDocumentType(documentType);
        filter.setMinBasePrice(minBasePrice);
        filter.setMaxBasePrice(maxBasePrice);
        filter.setMinFinalPrice(minFinalPrice);
        filter.setMaxFinalPrice(maxFinalPrice);
        filter.setMinAccessCount(minAccessCount);
        filter.setMaxAccessCount(maxAccessCount);
        filter.setCreatedInLastDay(createdInLastDay);

        Page<DocumentDTO> documents = documentService.getDocumentsWithFilters(filter, pageable);

        return ResponseEntity.ok(
                ApiResponse.success("Documents retrieved successfully", documents));
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "Update document",
            description = "Updates a document's title, description, or folder.")
    public ResponseEntity<ApiResponse<DocumentDTO>> updateDocument(
            @PathVariable Long id,
            @RequestBody DocumentUpdateDTO requestDTO) { // Changed back to DocumentRequestDTO
        DocumentDTO updatedDocument = documentService.updateDocument(id, requestDTO);
        return ResponseEntity.ok(
                ApiResponse.success("Document updated successfully", updatedDocument));
    }

    @PutMapping("/{documentId}/move")
    @Operation(summary = "Move document", description = "Moves a document to a different folder.")
    public ResponseEntity<ApiResponse<DocumentDTO>> moveDocument(
            @PathVariable Long documentId, @RequestParam Long newFolderId) {
        DocumentDTO movedDocument = documentService.moveDocument(documentId, newFolderId);
        return ResponseEntity.ok(ApiResponse.success("Document moved successfully", movedDocument));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete document", description = "Deletes a document by ID")
    public ResponseEntity<String> deleteDocument(@PathVariable Long id) {
        documentService.deleteDocument(id);
        return ResponseEntity.ok("Document deleted successfully!");
    }
}
