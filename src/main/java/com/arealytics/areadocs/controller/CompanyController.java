package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.CompanyFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.CompanyRequestMapper;
import com.arealytics.areadocs.service.CompanyMembershipService;
import com.arealytics.areadocs.service.CompanyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/companies")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Company Controller", description = "APIs for managing companies")
public class CompanyController {

    private final CompanyService companyService;
    private final CompanyMembershipService companyMembershipService;
    private final CompanyRequestMapper companyRequestMapper;

    @PostMapping
    @Operation(
            summary = "Create a new company",
            description =
                    "Creates a new company with the provided details. Required fields: name."
                        + " Optional fields: registrationNumber, industry, description, website,"
                        + " employeeCount. Only these fields are accepted.")
    public ResponseEntity<ApiResponse<CompanyDTO>> createCompany(
            @Valid @RequestBody CompanyRequestDTO companyRequestDTO) {
        try {
            CompanyDTO createdCompany = companyService.createCompany(companyRequestDTO);
            return new ResponseEntity<>(
                    ApiResponse.success("Company created successfully", createdCompany),
                    HttpStatus.CREATED);
        } catch (CompanyException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while creating the company: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping
    @Operation(
            summary = "Get companies",
            description = "Retrieves companies based on provided filters with pagination")
    public ResponseEntity<ApiResponse<Page<CompanyDTO>>> getCompanies(
            @Parameter(description = "Company ID") @RequestParam(required = false) Long companyId,
            @Parameter(description = "Company name") @RequestParam(required = false) String name,
            @Parameter(description = "ABN") @RequestParam(required = false) String abn,
            @Parameter(description = "ACN") @RequestParam(required = false) String acn,
            @Parameter(description = "Billing Email") @RequestParam(required = false)
                    String billingEmail,
            @Parameter(description = "Accounts Contact Name") @RequestParam(required = false)
                    String accountsContactName,
            @Parameter(description = "Accounts Contact Number") @RequestParam(required = false)
                    String accountsContactNumber,
            @Parameter(description = "Industry") @RequestParam(required = false) String industry,
            @Parameter(description = "Minimum employee count") @RequestParam(required = false)
                    Integer minEmployees,
            @Parameter(description = "Maximum employee count") @RequestParam(required = false)
                    Integer maxEmployees,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field and direction (e.g., createdAt,desc)")
                    @RequestParam(defaultValue = "createdAt,desc")
                    String sort) {

        try {
            if (companyId != null) {
                Optional<CompanyDTO> companyOpt = companyService.findCompanyById(companyId);
                if (companyOpt.isPresent()) {
                    Page<CompanyDTO> companyPage =
                            new org.springframework.data.domain.PageImpl<>(
                                    Collections.singletonList(companyOpt.get()));
                    return ResponseEntity.ok(ApiResponse.success(companyPage));
                } else {
                    Page<CompanyDTO> emptyPage = Page.empty();
                    return ResponseEntity.ok(ApiResponse.success(emptyPage));
                }
            }

            String[] sortParams = sort.split(",");
            Sort.Direction direction =
                    sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                            ? Sort.Direction.DESC
                            : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortParams[0]));

            CompanyFilterDTO filter = new CompanyFilterDTO();
            filter.setName(name);
            filter.setABN(abn);
            filter.setACN(acn);
            filter.setIndustry(industry);
            filter.setMinEmployeeCount(minEmployees);
            filter.setMaxEmployeeCount(maxEmployees);
            filter.setBillingEmail(billingEmail);
            filter.setAccountsContactName(accountsContactName);
            filter.setAccountsContactNumber(accountsContactNumber);

            Page<CompanyDTO> companies = companyService.getCompaniesWithFilters(filter, pageable);
            return ResponseEntity.ok(ApiResponse.success(companies));
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while retrieving companies: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "Update a company",
            description =
                    "Updates a company with the provided details including primary and billing"
                            + " addresses. Required fields: name, primaryAddress, billingAddress."
                            + " Optional fields: abn, acn, industry, description, website,"
                            + " employeeCount, isBillingPrimary, accountsContactName,"
                            + " accountsContactNumber, billingEmail.")
    public ResponseEntity<ApiResponse<CompanyDTO>> updateCompany(
            @Parameter(description = "Company ID", required = true) @PathVariable Long id,
            @Valid @RequestBody CompanyRequestDTO companyRequestDTO) {
        try {
            CompanyDTO updatedCompany = companyService.updateCompany(id, companyRequestDTO);
            return ResponseEntity.ok(
                    ApiResponse.success("Company updated successfully", updatedCompany));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (CompanyException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while updating the company: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a company", description = "Deletes a company by its ID")
    public ResponseEntity<ApiResponse<Void>> deleteCompany(
            @Parameter(description = "Company ID", required = true) @PathVariable Long id) {
        try {
            companyService.deleteCompany(id);
            return ResponseEntity.ok(ApiResponse.success("Company deleted successfully"));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while deleting the company: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PatchMapping("/{id}/active")
    @Operation(
            summary = "Toggle company active status",
            description = "Activates or deactivates a company by its ID using the 'isActive' query")
    public ResponseEntity<ApiResponse<CompanyDTO>> updateCompanyActiveStatus(
            @Parameter(description = "Company ID", required = true) @PathVariable Long id,
            @Parameter(
                            description = "Set company active or inactive",
                            required = true,
                            example = "true")
                    @RequestParam
                    Boolean isActive) {

        try {
            log.info("Received request to update company {} active status to: {}", id, isActive);
            CompanyDTO updatedCompany = companyService.updateCompanyActiveStatus(id, isActive);
            log.info("Successfully updated company {} active status", id);
            return ResponseEntity.ok(
                    ApiResponse.success(
                            "Company active status updated successfully", updatedCompany));
        } catch (EntityNotFoundException e) {
            log.error("Company not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (CompanyException e) {
            log.error("Company exception: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error updating company active status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(
                            ApiResponse.error(
                                    "An error occurred while updating company status: "
                                            + e.getMessage()));
        }
    }
}
