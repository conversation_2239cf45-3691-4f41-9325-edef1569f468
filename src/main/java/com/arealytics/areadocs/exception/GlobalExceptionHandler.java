package com.arealytics.areadocs.exception;

import java.util.HashMap;
import java.util.Map;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.arealytics.areadocs.dto.responseDTO.ApiResponse;

import jakarta.persistence.EntityNotFoundException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleEntityNotFoundException(
            EntityNotFoundException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(
            IllegalArgumentException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CompanyException.class)
    public ResponseEntity<ApiResponse<Object>> handleCompanyException(CompanyException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(PermissionException.PermissionNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handlePermissionNotFoundException(
            PermissionException.PermissionNotFoundException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(PermissionException.DuplicatePermissionCodeException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicatePermissionCodeException(
            PermissionException.DuplicatePermissionCodeException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(RoleException.RoleNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleRoleNotFoundException(
            RoleException.RoleNotFoundException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(RoleException.DuplicateRoleNameException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateRoleNameException(
            RoleException.DuplicateRoleNameException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataIntegrityViolationException(
            DataIntegrityViolationException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(
                        "Data integrity violation: " + ex.getMostSpecificCause().getMessage()),
                HttpStatus.CONFLICT);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult()
                .getAllErrors()
                .forEach(
                        error -> {
                            String fieldName = ((FieldError) error).getField();
                            String errorMessage = error.getDefaultMessage();
                            errors.put(fieldName, errorMessage);
                        });
        return new ResponseEntity<>(
                ApiResponse.error("Validation failed", errors), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex) {
        return new ResponseEntity<>(
                ApiResponse.error("An unexpected error occurred: " + ex.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // FolderNameConflictException
    @ExceptionHandler(FolderNameConflictException.class)
    public ResponseEntity<ApiResponse<Object>> handleFolderNameConflictException(
            FolderNameConflictException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(FolderCreationException.class)
    public ResponseEntity<ApiResponse<Object>> handleFolderCreationException(
            FolderCreationException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(ex.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(FolderUpdateException.class)
    public ResponseEntity<ApiResponse<Object>> handleFolderUpdateException(
            FolderUpdateException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(ex.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // DocumentFolderNotFoundException
    @ExceptionHandler(DocumentFolderNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleDocumentFolderNotFoundException(
            DocumentFolderNotFoundException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    // UnauthorizedException
    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<ApiResponse<Object>> handleUnauthorizedException(
            UnauthorizedException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.FORBIDDEN);
    }

    // UnsupportedOperationException
    @ExceptionHandler(UnsupportedOperationException.class)
    public ResponseEntity<ApiResponse<Object>> handleUnsupportedOperationException(
            UnsupportedOperationException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.FORBIDDEN);
    }

    // FolderNameConflictException
    @ExceptionHandler(FolderDeletionException.class)
    public ResponseEntity<ApiResponse<Object>> handleFolderDeletionException(
            FolderDeletionException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    // SystemFolderCreationException
    @ExceptionHandler(SystemFolderCreationException.class)
    public ResponseEntity<ApiResponse<Object>> handleSystemFolderCreationException(
            SystemFolderCreationException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    // UserUpdateException
    @ExceptionHandler(UserUpdateException.class)
    public ResponseEntity<ApiResponse<Void>> handleUserUpdateException(UserUpdateException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(
                        "An error occurred while updating user status: " + ex.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // InvalidTransactionException
    @ExceptionHandler(InvalidTransactionException.class)
    public ResponseEntity<ApiResponse<Void>> handleInvalidTransactionException(
            InvalidTransactionException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    // DocumentTitleConflictException
    @ExceptionHandler(DocumentTitleConflictException.class)
    public ResponseEntity<ApiResponse<Void>> handleDocumentTitleConflictException(
            DocumentTitleConflictException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.CONFLICT);
    }

    // FailedToUpdateOrderStatusException
    @ExceptionHandler(FailedToUpdateOrderStatusException.class)
    public ResponseEntity<ApiResponse<Void>> handleFailedToUpdateOrderStatusException(
            FailedToUpdateOrderStatusException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(ex.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(KeycloakException.UserNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleKeycloakUserNotFoundException(
            KeycloakException.UserNotFoundException ex) {
        return new ResponseEntity<>(ApiResponse.error(ex.getMessage()), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(KeycloakException.UserStatusUpdateException.class)
    public ResponseEntity<ApiResponse<Object>> handleKeycloakUserStatusUpdateException(
            KeycloakException.UserStatusUpdateException ex) {
        return new ResponseEntity<>(
                ApiResponse.error(ex.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
