package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.arealytics.areadocs.enumeration.SERV.LotType;
import com.arealytics.areadocs.enumeration.SERV.ParcelStatus;
import com.arealytics.areadocs.enumeration.SERV.ParcelType;
import com.arealytics.areadocs.enumeration.SERV.TitleStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomParcelResponse {
    @JsonProperty("spi")
    private String parcelIdentifier;

    @JsonProperty("parcelType")
    private ParcelType parcelType;

    @JsonProperty("lotType")
    private LotType lotType;

    @JsonProperty("parcelStatus")
    private ParcelStatus parcelStatus;

    @JsonProperty("parcelDetails")
    private ParcelDetails parcelDetails;

    @JsonProperty("titles")
    private List<ParcelTitle> titles;

    @JsonProperty("properties")
    private List<PropertyResponse.PropertyDTO> properties;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParcelDetails {
        @JsonProperty("lot")
        private String lot;

        @JsonProperty("planNumber")
        private String planNumber;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParcelTitle {
        @JsonProperty("titleId")
        private String titleId;

        @JsonProperty("titleStatus")
        private TitleStatus titleStatus;

        @JsonProperty("titleType")
        private TitleType titleType;
    }
}
