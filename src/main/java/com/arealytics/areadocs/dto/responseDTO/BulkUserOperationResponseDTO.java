package com.arealytics.areadocs.dto.responseDTO;

import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response DTO for bulk user operations")
public class BulkUserOperationResponseDTO {

    @Schema(
            description = "Map of username to operation result",
            example = "{\"<EMAIL>\": \"disabled\", \"<EMAIL>\": \"not found\"}")
    private Map<String, String> results;

    @Schema(description = "Total number of users processed", example = "2")
    private int totalProcessed;

    @Schema(description = "Number of successful operations", example = "1")
    private int successCount;

    @Schema(description = "Number of failed operations", example = "1")
    private int failureCount;

    public BulkUserOperationResponseDTO(Map<String, String> results) {
        this.results = results;
        this.totalProcessed = results.size();
        this.successCount =
                (int)
                        results.values().stream()
                                .filter(
                                        result ->
                                                result.equals("enabled")
                                                        || result.equals("disabled"))
                                .count();
        this.failureCount = totalProcessed - successCount;
    }
}
