package com.arealytics.areadocs.dto.responseDTO.serv;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

@Data
public class CustomProductResponse {

    private List<ProductDetails> products;

    @Data
    public static class ProductDetails {
        private String productCode;
        private String productName;
        private String productDescription;
        private String productAvailability;
        private BigDecimal price;
        private BigDecimal gst;
        private String documentType;
        private int turnaroundTime;
        private String turnaroundTimeUnit;
        private String customerWarning;
    }
}
