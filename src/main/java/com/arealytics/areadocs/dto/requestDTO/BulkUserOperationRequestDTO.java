package com.arealytics.areadocs.dto.requestDTO;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request DTO for bulk user operations (enable/disable)")
public class BulkUserOperationRequestDTO {

    @NotEmpty(message = "Usernames list cannot be empty") private List<String> usernames;

    @NotNull(message = "Operation cannot be null") @Pattern(
            regexp = "^(enable|disable)$",
            message = "Operation must be either 'enable' or 'disable'")
    @Schema(
            description = "Operation to perform",
            allowableValues = {"enable", "disable"},
            example = "disable")
    private String operation;
}
