#!/bin/bash

# Test script for company activation/deactivation endpoint
# Usage: ./test-company-endpoint.sh <company_id> <true|false>

COMPANY_ID=${1:-1}
IS_ACTIVE=${2:-false}
BASE_URL="http://localhost:8082"

echo "Testing company endpoint..."
echo "Company ID: $COMPANY_ID"
echo "Setting isActive to: $IS_ACTIVE"
echo ""

# Make the API call
curl -X PATCH "$BASE_URL/companies/$COMPANY_ID/active?isActive=$IS_ACTIVE" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -v

echo ""
echo "Test completed."
